{"name": "bankk", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "formik": "^2.4.6", "i18next": "^22.5.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "react-select": "^5.10.1", "react-tabs": "^6.1.0", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@stagewise/toolbar-react": "^0.4.1"}}