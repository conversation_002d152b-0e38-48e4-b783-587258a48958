.biometrics-container {
  padding: 1rem;
}

.biometrics-container h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.biometrics-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.biometrics-section {
  background-color: var(--light-gray);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.biometrics-section h3 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--medium-gray);
  padding-bottom: 0.5rem;
}

.fingerprint-placeholder,
.photo-placeholder,
.signature-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: white;
  border: 2px dashed var(--medium-gray);
  border-radius: 4px;
  padding: 1rem;
  text-align: center;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.implementation-note {
  margin-top: 2rem;
  padding: 1rem;
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid var(--warning-color);
  border-radius: 4px;
}

@media (max-width: 768px) {
  .biometrics-content {
    grid-template-columns: 1fr;
  }
} 