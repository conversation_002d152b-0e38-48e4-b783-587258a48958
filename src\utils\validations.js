/**
 * Validates an email address
 * @param {string} email Email to validate
 * @returns {boolean} True if email is valid
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates a PAN (Permanent Account Number)
 * @param {string} pan PAN to validate
 * @returns {boolean} True if PAN is valid
 */
export const isValidPAN = (pan) => {
  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
  return panRegex.test(pan);
};

/**
 * Validates a UID (Aadhaar Number)
 * @param {string} uid UID to validate
 * @returns {boolean} True if UID is valid
 */
export const isValidUID = (uid) => {
  const uidRegex = /^[2-9]{1}[0-9]{11}$/;
  return uidRegex.test(uid);
};

/**
 * Validates a mobile number
 * @param {string} mobile Mobile number to validate
 * @returns {boolean} True if mobile number is valid
 */
export const isValidMobile = (mobile) => {
  const mobileRegex = /^[6-9]\d{9}$/;
  return mobileRegex.test(mobile);
};

/**
 * Validates a PIN code
 * @param {string} pinCode PIN code to validate
 * @returns {boolean} True if PIN code is valid
 */
export const isValidPinCode = (pinCode) => {
  const pinCodeRegex = /^[1-9][0-9]{5}$/;
  return pinCodeRegex.test(pinCode);
}; 