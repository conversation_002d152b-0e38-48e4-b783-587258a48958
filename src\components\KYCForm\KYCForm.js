import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import IdentityDetails from './IdentityDetails';
import AddressInformation from './AddressInformation';
import ContactDetails from './ContactDetails';
import PANUIDSection from './PANUIDSection';
import FormActions from './FormActions';
import { saveKYCRecord, searchKYCRecords } from '../../services/storage';
import { isValidPAN, isValidUID, isValidMobile, isValidEmail, isValidPinCode } from '../../utils/validations';
import '../../styles/KYCForm.css';

const KYCForm = () => {
  const { t } = useTranslation();
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [selectedRecord, setSelectedRecord] = useState(null);

  // Initial values for the form
  const initialValues = {
    // Identity Details
    type: '',
    kycId: '',
    lastName: '',
    firstName: '',
    surname: '',
    mobileNumber: '',
    
    // Name Builder
    prefix: '',
    builtLastName: '',
    builtFirstName: '',
    builtSecondName: '',
    sex: 'male',
    occur: '0',
    sahTitle1: '',
    sahTitle2: '',
    fullNameDisplay: '',
    
    // Address Information
    state: '',
    district: '',
    taluka: '',
    city: '',
    pinCode: '',
    correspondenceAddress: '',
    permanentAddress: '',
    
    // Contact Details
    stdCode: '',
    mobileNumbers: [''],
    isSmartPhone: [false],
    landline: '',
    email: '',
    website: '',
    
    // PAN/UID
    pan: '',
    uid: ''
  };

  // Validation schema using Yup
  const validationSchema = Yup.object({
    // Identity Details validation
    lastName: Yup.string().required(t('validation.required')),
    firstName: Yup.string().required(t('validation.required')),
    mobileNumber: Yup.string()
      .test('is-valid-mobile', t('validation.invalidMobile'), 
        value => !value || isValidMobile(value)),
    
    // Address Information validation
    pinCode: Yup.string()
      .test('is-valid-pincode', 'Invalid PIN code', 
        value => !value || isValidPinCode(value)),
    
    // Contact Details validation
    email: Yup.string()
      .test('is-valid-email', t('validation.invalidEmail'), 
        value => !value || isValidEmail(value)),
    
    // PAN/UID validation
    pan: Yup.string()
      .test('is-valid-pan', t('validation.invalidPan'), 
        value => !value || isValidPAN(value)),
    uid: Yup.string()
      .test('is-valid-uid', t('validation.invalidUid'), 
        value => !value || isValidUID(value))
  });

  // Handle form submission
  const handleSubmit = (values, { resetForm }) => {
    if (isSearchMode) {
      // Handle search operation
      const results = searchKYCRecords(values);
      setSearchResults(results);
    } else {
      // Handle save operation
      saveKYCRecord(values);
      resetForm();
      alert('KYC form saved successfully!');
    }
  };

  // Toggle between search and data entry modes
  const toggleMode = () => {
    setIsSearchMode(!isSearchMode);
    setSearchResults([]);
    setSelectedRecord(null);
  };

  // Clear form
  const handleClearForm = (resetForm) => {
    resetForm();
    setSearchResults([]);
    setSelectedRecord(null);
  };

  // Handle selecting a record from search results
  const handleSelectRecord = (record) => {
    setSelectedRecord(record);
  };

  return (
    <div className="kyc-form-container">
      <Formik
        initialValues={selectedRecord || initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {(formikProps) => (
          <Form>
            <IdentityDetails 
              isSearchMode={isSearchMode} 
              searchResults={searchResults}
              onSelectRecord={handleSelectRecord}
            />
            
            <div className="address-contact-row">
              <AddressInformation />
              <ContactDetails />
            </div>
            
            <PANUIDSection />
            
            <FormActions 
              isSearchMode={isSearchMode} 
              toggleMode={toggleMode}
              onClearForm={() => handleClearForm(formikProps.resetForm)}
            />
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default KYCForm; 