import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Field, useFormikContext } from 'formik';
import CreatableSelect from 'react-select/creatable';

// English-to-Marathi mapping for name display
const MARATHI_MAP = {
  prefix: {
    'Shri': 'श्रीमान.',
    'Mr': 'श्री.',
    'Mrs': 'श्रीमती.',
    'Ms': 'कुमारी.',
    'Dr': 'डॉ.',
  },
  builtLastName: {
    'Hajare': 'हजारे',
    'Patel': 'पटेल',
    'Sharma': 'शर्मा',
    'Khan': 'खान',
    'Singh': 'सिंह',
    'Kumar': 'कुमार',
    'Das': 'दास',
    'Gupta': 'गुप्ता',
    'Joshi': 'जोशी',
  },
  builtFirstName: {
    'Baba': 'बाबा',
    'Ra<PERSON>': 'राहुल',
    'Priya': 'प्रिया',
    'Amit': 'अमित',
    'Neha': 'नेहा',
    'Raj': 'राज',
    'Pooja': 'पूजा',
    'Anil': 'अनिल',
    'Smita': 'स्मिता',
  },
  sahTitle1: {
    'saheb': 'साहेब',
    'Saheb': 'साहेब',
    'Baba': 'बाबा',
    'Babahari': 'बाबाहरी',
    'Babai': 'बाबाई',
    'Babali': 'बाबली',
    'Babalu': 'बाबालु',
    'Baban': 'बाबन',
    'Babanai': 'बाबनाई',
  },
  builtSecondName: {
    'Karbari': 'कार्यकारी',
    'Kumar': 'कुमार',
    'Devi': 'देवी',
    'Prasad': 'प्रसाद',
    'Kumari': 'कुमारी',
    'Dev': 'देव',
    'Lal': 'लाल',
    'Chand': 'चंद',
  },
  sahTitle2: {
    'Karbari': 'कार्यकारी',
    'saheb': 'साहेब',
    'Saheb': 'साहेब',
    'Baba': 'बाबा',
    'Babahari': 'बाबाहरी',
    'Babai': 'बाबाई',
    'Babali': 'बाबली',
    'Babalu': 'बाबालु',
    'Baban': 'बाबन',
    'Babanai': 'बाबनाई',
  },
};

const IdentityDetails = ({ isSearchMode, searchResults, onSelectRecord }) => {
  const { t } = useTranslation();
  const { values, setFieldValue } = useFormikContext();
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [kycFound, setKycFound] = useState(false);
  const [showNameBuilder, setShowNameBuilder] = useState(false);
  const [showCreateNewDropdown, setShowCreateNewDropdown] = useState(false);
  const createNewRef = useRef(null);
  const [showAdditionalIdentityDetails, setShowAdditionalIdentityDetails] = useState(false);
  const [isNameBuilderActive, setIsNameBuilderActive] = useState(false);
  const [isKycSearchActive, setIsKycSearchActive] = useState(false);

  // Add refs for birth and anniversary date fields
  const birthDayRef = useRef();
  const birthMonthRef = useRef();
  const birthYearRef = useRef();
  const annivDayRef = useRef();
  const annivMonthRef = useRef();
  const annivYearRef = useRef();

  // Handler for auto-focus
  const handleDateInput = (e, maxLength, nextRef) => {
    if (e.target.value.length >= maxLength && nextRef && nextRef.current) {
      nextRef.current.focus();
    }
  };

  // Handler for moving focus to next field on Enter
  const handleEnterFocusNext = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const form = e.target.form;
      const index = Array.prototype.indexOf.call(form, e.target);
      for (let i = index + 1; i < form.elements.length; i++) {
        const el = form.elements[i];
        if ((el.tagName === 'INPUT' || el.tagName === 'SELECT') && !el.disabled && el.offsetParent !== null) {
          el.focus();
          break;
        }
      }
    }
  };

  // Function to handle search (Go button)
  const handleSearch = (e) => {
    e.preventDefault();
    setSearchPerformed(true);
    // Simulate search result - in real app, this would check against a database
    setKycFound(false); // For demo purpose, always show "Create New"
  };

  // Function to handle Ok button in KYC Search - should show Name Builder
  const handleShowNameBuilder = () => {
    setShowNameBuilder(true);
    // Do NOT show additional details yet
  };

  // Function to handle Ok button in Name Builder - should show Additional Details and populate Father/Spouse
  const handleNameBuilderOk = () => {
    // Get built last name and second name
    const builtLastName = values.builtLastName || '';
    const builtSecondName = values.builtSecondName || '';

    // Combine them for the Father/Spouse field
    const fatherSpouseValue = `${builtLastName} ${builtSecondName}`.trim();

    // Set the value of the fatherSpouse field
    setFieldValue('fatherSpouse', fatherSpouseValue);

    // Then show the additional details section
    setShowAdditionalIdentityDetails(true);
  };

  // Function to toggle the Create New dropdown
  const toggleCreateNewDropdown = () => {
    setShowCreateNewDropdown(!showCreateNewDropdown);
  };
  
  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (createNewRef.current && !createNewRef.current.contains(event.target)) {
        setShowCreateNewDropdown(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update the full name display whenever name components change
  useEffect(() => {
    if (showNameBuilder) {
      // Compose the Marathi display name
      const marathiName = [
        MARATHI_MAP.prefix[values.prefix] || values.prefix,
        MARATHI_MAP.builtLastName[values.builtLastName] || values.builtLastName,
        MARATHI_MAP.builtFirstName[values.builtFirstName] || values.builtFirstName,
        MARATHI_MAP.sahTitle1[values.sahTitle1] || values.sahTitle1,
        MARATHI_MAP.builtSecondName[values.builtSecondName] || values.builtSecondName,
        MARATHI_MAP.sahTitle2[values.sahTitle2] || values.sahTitle2,
      ]
        .filter(Boolean)
        .join(' ')
        .trim()
        .replace(/\n/g, ' ')
        .replace(/\s+/g, ' ');
      setFieldValue('fullNameDisplay', marathiName);
    }
  }, [values.prefix, values.builtLastName, values.builtFirstName, values.sahTitle1, values.builtSecondName, values.sahTitle2, setFieldValue, showNameBuilder]);

  // Function to handle change in name fields
  const updateNameDisplay = () => {
    // Ensure we're returning a simple string with no HTML/JSX that could cause line breaks
    return values.fullNameDisplay || '';
  };

  // Sample name options - in a real app, these would come from a database or API
  const lastNameOptions = ["Sharma", "Patel", "Khan", "Singh", "Kumar", "Das", "Gupta", "Joshi"].map(name => ({ label: name, value: name }));
  const firstNameOptions = ["Rahul", "Priya", "Amit", "Neha", "Raj", "Pooja", "Anil", "Smita"].map(name => ({ label: name, value: name }));
  const secondNameOptions = ["Kumar", "Devi", "Prasad", "Kumari", "Dev", "Lal", "Chand"].map(name => ({ label: name, value: name }));

  const sahTitleOptions = [
    { label: 'सह-शिर्षक 1', value: 'सह-शिर्षक 1' },
    { label: 'सह-शिर्षक 2', value: 'सह-शिर्षक 2' },
    { label: 'B.', value: 'B.' },
    { label: 'Baaban', value: 'Baaban' },
    { label: 'Bab', value: 'Bab' },
    { label: 'Baba', value: 'Baba' },
    { label: 'Babahari', value: 'Babahari' },
    { label: 'Babai', value: 'Babai' },
    { label: 'Babali', value: 'Babali' },
    { label: 'Babalu', value: 'Babalu' },
    { label: 'Baban', value: 'Baban' },
    { label: 'Babanai', value: 'Babanai' },
  ];

  // Custom styles for react-select to match the form and screenshot
  const customSelectStyles = {
    control: (provided) => ({
      ...provided,
      minHeight: '28px',
      height: '28px',
      border: '1.5px solid #888',
      boxShadow: 'none',
      fontSize: '1rem',
      backgroundColor: 'white',
      padding: 0,
      margin: 0,
      borderRadius: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      '&:hover': {
        border: '1.5px solid #888',
      }
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 10,
      backgroundColor: 'white',
      border: '1.5px solid #888',
      borderRadius: 0,
      fontSize: '1rem',
      marginTop: 0,
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected ? '#0000ff' : state.isFocused ? '#e6e6ff' : 'white',
      color: state.isSelected ? 'white' : '#222',
      cursor: 'pointer',
      fontSize: '1rem',
      padding: '4px 6px',
    }),
    singleValue: (provided) => ({
      ...provided,
      color: '#222',
      fontSize: '1rem',
      margin: 0,
      padding: 0,
    }),
    input: (provided) => ({
      ...provided,
      color: '#222',
      margin: 0,
      padding: 0,
    }),
    valueContainer: (provided) => ({
      ...provided,
      padding: '0 4px',
      margin: 0,
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      padding: 2,
      color: '#333333',
      backgroundColor: '#d0d0d0',
      borderLeft: '1px solid #888',
      '&:hover': {
        color: '#333333'
      }
    }),
    clearIndicator: (provided) => ({
      ...provided,
      padding: 2,
    }),
    indicatorSeparator: () => ({ display: 'none' }),
    placeholder: (provided) => ({
      ...provided,
      color: '#666',
      margin: 0,
      padding: 0,
    }),
  };

  // Handle selecting an item from the Create New dropdown
  const handleCreateNewSelect = (type) => {
    setFieldValue('type', type);
    setShowCreateNewDropdown(false);
    handleShowNameBuilder(); // Trigger showing the Name Builder when a type is selected
  };

  return (
    <div className="form-section">
      <h3 className="form-section-title">
        1. {t('identityDetails.title')} <span className="bilingual-text">परिचय-वर्णन</span>
      </h3>
      
      <div className="form-section-content identity-section">
        <div className="identity-search-area">
          <div className="search-header">
            <button
              className={`search-button${isKycSearchActive ? ' active' : ''}`}
              onClick={() => setIsKycSearchActive(!isKycSearchActive)}
            >
              KYC Search
            </button>
          </div>
          
          <div className="identity-search-grid">
            <div className="identity-header">
              <div className="identity-cell">Type</div>
              <div className="identity-cell">KYC-ID</div>
              <div className="identity-cell">L-Name</div>
              <div className="identity-cell">F-Name</div>
              <div className="identity-cell">S-Name</div>
              <div className="identity-cell">Mobile-No.</div>
            </div>
            
            <div className="identity-row">
              <div className="identity-cell">
                <Field
                  id="type"
                  name="type"
                  as="select"
                  className="form-control"
                  onKeyDown={handleEnterFocusNext}
                >
                  <option value="">-</option>
                  <option value="individual">Individual</option>
                  <option value="corporate">Corporate</option>
                  <option value="trust">Trust</option>
                </Field>
              </div>
              
              <div className="identity-cell">
                <Field
                  id="kycId"
                  name="kycId"
                  type="text"
                  className="form-control"
                  onKeyDown={handleEnterFocusNext}
                />
              </div>
              
              <div className="identity-cell">
                <Field
                  id="lastName"
                  name="lastName"
                  type="text"
                  className="form-control"
                  onKeyDown={handleEnterFocusNext}
                />
              </div>
              
              <div className="identity-cell">
                <Field
                  id="firstName"
                  name="firstName"
                  type="text"
                  className="form-control"
                  onKeyDown={handleEnterFocusNext}
                />
              </div>
              
              <div className="identity-cell">
                <Field
                  id="surname"
                  name="surname"
                  type="text"
                  className="form-control"
                  onKeyDown={handleEnterFocusNext}
                />
              </div>
              
              <div className="identity-cell">
                <Field
                  id="mobileNumber"
                  name="mobileNumber"
                  type="text"
                  className="form-control"
                  onKeyDown={handleEnterFocusNext}
                />
              </div>
            </div>
            <div className="identity-button-row">
              <div className="button-cell"></div>
              <div className="button-cell"></div>
              <div className="button-cell"></div>
              <div className="button-cell">
                {searchPerformed && !kycFound && (
                  <div className="create-new-dropdown" ref={createNewRef}>
                    <button 
                      type="button" 
                      className="id-button create-new" 
                      onClick={toggleCreateNewDropdown}
                    >
                      Create New
                      <span className="dropdown-arrow">▼</span>
                    </button>
                    {showCreateNewDropdown && (
                      <div className="create-new-menu">
                        <div className="dropdown-item" onClick={() => handleCreateNewSelect('individual')}>Individual</div>
                        <div className="dropdown-item" onClick={() => handleCreateNewSelect('corporate')}>Corporate</div>
                        <div className="dropdown-item" onClick={() => handleCreateNewSelect('trust')}>Trust</div>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="button-cell">
                <button type="button" className="id-button" onClick={handleSearch}>Go</button>
              </div>
              <div className="button-cell">
                <button type="button" className="id-button" onClick={handleShowNameBuilder}>Ok</button>
              </div>
            </div>
          </div>
        </div>
        
        {showNameBuilder && (
          <div className="name-builder-area">
            <div className="name-builder-header">
              <button 
                className={`name-builder-title ${isNameBuilderActive ? 'active' : ''}`}
                onClick={() => setIsNameBuilderActive(!isNameBuilderActive)}
              >
                Individual Name Builder
              </button>
              <div className={`name-builder-sex ${!isNameBuilderActive ? 'disabled' : ''}`}>
                Sex <span className="bilingual-text">लिंग</span>
                <Field
                  as="select"
                  name="sex"
                  className="sex-select custom-dropdown"
                  disabled={!isNameBuilderActive}
                >
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="neuter">Neuter</option>
                </Field>
              </div>
              <div className="name-builder-occur">
                Occur <Field 
                  name="occur" 
                  type="text" 
                  className="counter" 
                  value="0"
                  disabled={!isNameBuilderActive}
                />
              </div>
            </div>
            
            <div className={`name-builder-grid ${!isNameBuilderActive ? 'disabled' : ''}`}>
              <div className="name-labels-row">
                <div className="name-label title-label">
                  <span className="english-label">Title/</span>श्रीवाचक
                </div>
                <div className="name-label name-label-main">
                  <span className="english-label">Last-Name </span>कुलनाम
                </div>
                <div className="separator">+</div>
                <div className="name-label name-label-main">
                  <span className="english-label">First-Name </span>नाम
                </div>
                <div className="separator-text">सह-शिर्षक</div>
                <div className="name-label name-label-main">
                  <span className="english-label">Second-Name </span>उपनाम
                </div>
                <div className="separator-text">सह-शिर्षक</div>
              </div>
              
              <div className="name-values-row">
                <div className="prefix-select-wrapper">
                  <Field
                    as="select"
                    name="prefix"
                    className="form-control custom-dropdown"
                    disabled={!isNameBuilderActive}
                  >
                    <option value="">-</option>
                    <option value="Mr">Mr</option>
                    <option value="Mrs">Mrs</option>
                    <option value="Ms">Ms</option>
                    <option value="Dr">Dr</option>
                    <option value="Shri">Shri</option>
                  </Field>
                </div>
                <div className="name-input-wrapper">
                  <CreatableSelect
                    name="builtLastName"
                    options={lastNameOptions}
                    styles={customSelectStyles}
                    placeholder=""
                    value={values.builtLastName ? { label: values.builtLastName, value: values.builtLastName } : null}
                    onChange={option => setFieldValue('builtLastName', option ? option.value : '')}
                    isClearable
                    isSearchable
                    menuPlacement="auto"
                    isDisabled={!isNameBuilderActive}
                  />
                </div>
                <div className="separator">+</div>
                <div className="name-input-wrapper">
                  <CreatableSelect
                    name="builtFirstName"
                    options={firstNameOptions}
                    styles={customSelectStyles}
                    placeholder=""
                    value={values.builtFirstName ? { label: values.builtFirstName, value: values.builtFirstName } : null}
                    onChange={option => setFieldValue('builtFirstName', option ? option.value : '')}
                    isClearable
                    isSearchable
                    menuPlacement="auto"
                    isDisabled={!isNameBuilderActive}
                  />
                </div>
                <div className="name-input-wrapper">
                  <CreatableSelect
                    name="sahTitle1"
                    options={sahTitleOptions}
                    styles={customSelectStyles}
                    placeholder=""
                    value={values.sahTitle1 ? { label: values.sahTitle1, value: values.sahTitle1 } : null}
                    onChange={option => setFieldValue('sahTitle1', option ? option.value : '')}
                    isClearable
                    isSearchable
                    menuPlacement="auto"
                    isDisabled={!isNameBuilderActive}
                  />
                </div>
                <div className="name-input-wrapper">
                  <CreatableSelect
                    name="builtSecondName"
                    options={secondNameOptions}
                    styles={customSelectStyles}
                    placeholder=""
                    value={values.builtSecondName ? { label: values.builtSecondName, value: values.builtSecondName } : null}
                    onChange={option => setFieldValue('builtSecondName', option ? option.value : '')}
                    isClearable
                    isSearchable
                    menuPlacement="auto"
                    isDisabled={!isNameBuilderActive}
                  />
                </div>
                <div className="name-input-wrapper">
                  <CreatableSelect
                    name="sahTitle2"
                    options={sahTitleOptions}
                    styles={customSelectStyles}
                    placeholder=""
                    value={values.sahTitle2 ? { label: values.sahTitle2, value: values.sahTitle2 } : null}
                    onChange={option => setFieldValue('sahTitle2', option ? option.value : '')}
                    isClearable
                    isSearchable
                    menuPlacement="auto"
                    isDisabled={!isNameBuilderActive}
                  />
                </div>
              </div>
              
              <div className="name-output-footer-row">
                <div className="name-output-area">
                  <span className="name-display-text">{updateNameDisplay()}</span>
                </div>
                <div className="name-builder-footer">
                  <button type="button" className="id-button" onClick={handleNameBuilderOk} disabled={!isNameBuilderActive}>Ok</button>
                </div>
              </div>
            </div>
          </div>
        )}

        {showAdditionalIdentityDetails && (
          <div className="additional-identity-details-section">
            <div className="additional-identity-table">
              <div className="additional-identity-header-row">
                <span className="additional-label birth">Birth-Date</span>
                <span className="additional-label underlined">Marital-Status</span>
                <span className="additional-label father underlined">
                  Father/Spouse
                  <span className="additional-sublabel">(L-Name F-Name S-Name)</span>
                </span>
                <span className="additional-label underlined">Anniversary</span>
              </div>
              <div className="additional-identity-input-row">
                <div className="additional-date-group">
                  <Field
                    name="birthDateDay"
                    className="additional-input"
                    placeholder="DD"
                    maxLength={2}
                    innerRef={birthDayRef}
                    onChange={e => {
                      setFieldValue('birthDateDay', e.target.value);
                      handleDateInput(e, 2, birthMonthRef);
                    }}
                  />
                  <span className="vertical-separator">|</span>
                  <Field
                    name="birthDateMonth"
                    className="additional-input"
                    placeholder="MM"
                    maxLength={2}
                    innerRef={birthMonthRef}
                    onChange={e => {
                      setFieldValue('birthDateMonth', e.target.value);
                      handleDateInput(e, 2, birthYearRef);
                    }}
                  />
                  <span className="vertical-separator">|</span>
                  <Field
                    name="birthDateYear"
                    className="additional-input additional-input-year"
                    placeholder="YYYY"
                    maxLength={4}
                    innerRef={birthYearRef}
                    onChange={e => setFieldValue('birthDateYear', e.target.value)}
                  />
                </div>
                <div className="additional-marital-group">
                  <Field as="select" name="maritalStatus" className="additional-input" style={{minWidth: '80px'}}>
                    <option value="">-</option>
                    <option value="Married">Married</option>
                    <option value="Unmarried">Unmarried</option>
                    <option value="Widow">Widow</option>
                    <option value="Divorced">Divorced</option>
                  </Field>
                </div>
                <div className="additional-father-group">
                  <Field name="fatherSpouse" className="additional-input father-spouse-input" placeholder="" />
                </div>
                <div className="additional-date-group">
                  <Field
                    name="annivDay"
                    className="additional-input"
                    placeholder="DD"
                    maxLength={2}
                    innerRef={annivDayRef}
                    onChange={e => {
                      setFieldValue('annivDay', e.target.value);
                      handleDateInput(e, 2, annivMonthRef);
                    }}
                  />
                  <span className="vertical-separator">|</span>
                  <Field
                    name="annivMonth"
                    className="additional-input"
                    placeholder="MM"
                    maxLength={2}
                    innerRef={annivMonthRef}
                    onChange={e => {
                      setFieldValue('annivMonth', e.target.value);
                      handleDateInput(e, 2, annivYearRef);
                    }}
                  />
                  <span className="vertical-separator">|</span>
                  <Field
                    name="annivYear"
                    className="additional-input additional-input-year"
                    placeholder="YYYY"
                    maxLength={4}
                    innerRef={annivYearRef}
                    onChange={e => setFieldValue('annivYear', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IdentityDetails; 