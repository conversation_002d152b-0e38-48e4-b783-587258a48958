import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../i18n/LanguageContext';
import '../styles/Header.css';

const Header = () => {
  const { language, changeLanguage } = useLanguage();

  return (
    <header className="app-header">
      <div className="branding">
        <span className="app-version">VSP-CRED Ver: 1.0</span>
        <h1 className="app-title">KYC Form</h1>
      </div>
      <div className="language-switcher">
        <button 
          className={`language-btn ${language === 'en' ? 'active' : ''}`} 
          onClick={() => changeLanguage('en')}
        >
          English
        </button>
        <button 
          className={`language-btn ${language === 'hi' ? 'active' : ''}`} 
          onClick={() => changeLanguage('hi')}
        >
          हिंदी
        </button>
      </div>
    </header>
  );
};

export default Header; 