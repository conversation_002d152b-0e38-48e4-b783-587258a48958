@media print {
  /* Hide elements not needed for printing */
  .app-header,
  .language-switcher,
  .react-tabs__tab-list,
  .form-actions,
  .implementation-note,
  button {
    display: none !important;
  }
  
  /* Show only the active tab */
  .react-tabs__tab-panel:not(.react-tabs__tab-panel--selected) {
    display: none !important;
  }
  
  /* Adjust margins and remove background colors */
  body {
    margin: 0;
    padding: 0;
    background: white;
    font-size: 12pt;
  }
  
  .container {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0;
  }
  
  .form-section {
    page-break-inside: avoid;
    background-color: white !important;
    border: 1px solid #ccc;
    margin-bottom: 1em;
  }
  
  /* Add title for printing */
  .kyc-form-container::before {
    content: "KYC Form";
    display: block;
    font-size: 18pt;
    font-weight: bold;
    text-align: center;
    margin-bottom: 1em;
  }
  
  /* Adjust form layout for better printing */
  .form-row {
    display: flex;
    flex-wrap: wrap;
  }
  
  .form-group {
    flex: 1;
    min-width: 200px;
  }
  
  /* Add page margins */
  @page {
    margin: 2cm;
  }
  
  /* Improve readability of form elements */
  input, select, textarea {
    border: 1px solid #ccc !important;
    background-color: white !important;
  }
  
  /* Format validation messages */
  .form-error {
    color: black !important;
    font-style: italic;
  }
} 