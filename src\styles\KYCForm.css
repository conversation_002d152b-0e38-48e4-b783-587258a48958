/* Base styling */
form {
  width: 100%;
}

.kyc-form-container {
  max-width: 100%;
  width: 100%;
  background-color: white;
  border: 1px solid #ccc;
}

/* Form Section General Styling */
.form-section {
  margin-bottom: 0;
  border-bottom: 1px solid #ccc;
  width: 100%;
}

.form-section-title {
  background-color: #555;
  color: white;
  padding: 0.25rem 0.5rem;
  margin: 0;
  font-size: 0.9rem;
  font-weight: bold;
  border-bottom: 1px solid #000;
}

.form-section-content {
  padding: 0;
  background-color: #f0f0f0;
}

/* Identity Details Section */
.form-section:first-child {
  margin-bottom: 1rem;
  padding-left: 1rem;
}

.form-section:first-child .form-section-content {
  max-width: none;
  background-color: transparent;
  margin-left: 0;
  border: none;
  padding-left: 0;
}

.identity-section {
  display: flex;
  flex-wrap: wrap;
  gap: 0 24px;
}

.identity-search-area {
  width: 600px;
  background-color: #d8d8d8;
  border: 1px solid #999;
  height: 180px;
  display: flex;
  flex-direction: column;
}

.name-builder-area {
  background-color: #d8d8d8 !important;
  border: 1.5px solid #888 !important;
  min-width: 500px;
  width: 1000px;
  box-shadow: none;
  padding: 0;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.create-new {
  font-weight: bold;
  color: #006;
  position: relative;
  padding-right: 24px !important;
  text-align: left;
  width: auto;
  min-width: 110px;
  background: linear-gradient(to bottom, #f5f5f5, #e5e5e5) !important;
  border: 1px solid #999 !important;
}

.create-new-dropdown {
  position: relative;
  display: inline-block;
  z-index: 10;
}

.create-new-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 100%;
  width: 120px;
  background: white;
  border: 1px solid #999;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  z-index: 100;
}

.dropdown-item {
  padding: 6px 10px;
  cursor: pointer;
  white-space: nowrap;
  color: #006;
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background-color: #e8e8f8;
}

.dropdown-arrow {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 8px;
  color: #006;
}

/* Name Builder Styling */
.name-builder-header {
  display: flex;
  align-items: center;
  background-color: #d0d0d0 !important;
  border-bottom: 1.5px solid #888;
  border-top: 3px solid #0000cc;
  height: 32px;
  padding: 0 4px;
  font-size: 1rem;
  font-weight: bold;
  gap: 8px;
}

.name-builder-title {
  border: 2px solid #1976d2;         /* blue border always */
  background: #fff;
  color: #222;
  font-weight: bold;
  transition: all 0.2s;
  outline: none;
}

.name-builder-title:not(.active):hover,
.name-builder-title:not(.active):focus {
  background: #e3f0fd;               /* light blue on hover/focus */
  box-shadow: 0 0 0 2px #90caf9;
  cursor: pointer;
}

.name-builder-title.active {
  border-color: #0066ff;
  box-shadow: 0 0 5px #0066ff;
}

.name-builder-area.disabled {
  pointer-events: none;
  opacity: 0.7;
}

.name-builder-area.disabled input,
.name-builder-area.disabled select,
.name-builder-area.disabled .css-b62m3t-container {
  background-color: #f5f5f5 !important;
  border-color: #ccc !important;
  cursor: not-allowed;
}

.name-builder-sex {
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 0;
}

.sex-select {
  min-width: 80px;
  height: 28px;
  font-size: 1rem;
  border: 1.5px solid #888;
  background: #fff;
  padding: 0 2px;
  padding-left: 4px;
  box-sizing: border-box;
  margin-left: 5px;
}

.counter {
  height: 28px;
  min-width: 40px;
  font-size: 1rem;
  border: 1.5px solid #888;
  background: #fff;
  padding: 0 2px;
  box-sizing: border-box;
  text-align: center;
}

/* Name Builder Grid - Fixed column widths */
.name-builder-grid {
  background: #d8d8d8 !important;
  padding: 4px 4px 0 4px;
  width: 100%;
  display: table;
  border-spacing: 4px 2px;
  border-collapse: separate;
  position: relative;
  table-layout: fixed;
}

.name-labels-row {
  display: table-row;
  background: #d8d8d8 !important;
}

.name-values-row {
  display: table-row;
  background: #d8d8d8 !important;
}

.name-label, .title-label, .name-label-main, .separator, .separator-text,
.prefix-select-wrapper, .name-input-wrapper {
  display: table-cell;
  vertical-align: middle;
}

/* Cell Styles */
.title-label, .name-label, .name-label-main {
  padding: 0 2px;
  text-align: left;
  color: #222;
  font-size: 1rem;
  font-weight: normal;
  white-space: nowrap;
}

/* Explicitly set fixed column widths */
.title-label {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
}

.name-label-main {
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
}

.separator {
  width: 20px !important;
  min-width: 20px !important;
  max-width: 20px !important;
  text-align: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.separator-text {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: center;
  font-size: 0.9rem;
}

.prefix-select-wrapper {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  padding-bottom: 4px;
}

.name-input-wrapper {
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
  padding-bottom: 4px;
}

.bilingual-text {
  margin-left: 3px;
  color: #666;
}

/* Ensure the input fields don't affect column width */
.name-builder-area input[type="text"],
.name-builder-area select,
.name-builder-area .css-b62m3t-container {
  box-sizing: border-box;
  width: 100% !important;
  max-width: 100% !important;
}

/* Fix react-select specific styling */
.name-builder-area .css-b62m3t-container {
  width: 100%;
  margin: 0;
}

.name-builder-area input[type="text"],
.name-builder-area select {
  height: 28px !important;
  font-size: 1rem !important;
  border: 1.5px solid #888 !important;
  background: #fff !important;
  border-radius: 0 !important;
  padding: 0 2px !important;
  margin: 0 !important;
}

/* Prevent column width changes on focus or hover */
.name-builder-area .css-1dimb5e-singleValue,
.name-builder-area .css-1jqq78o-placeholder,
.name-builder-area .css-qbdosj-Input,
.name-builder-area .css-13cymwt-control:hover,
.name-builder-area .css-13cymwt-control:focus,
.name-builder-area .css-t3ipsp-control {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
}

/* Fix control widths to prevent expansion */
.name-builder-area .css-13cymwt-control {
  min-height: 28px;
  height: 28px;
  border: 1.5px solid #888;
  border-radius: 0;
  width: 100% !important;
  min-width: 0 !important;
  flex: 1 1 auto !important;
}

.name-builder-area .css-1dimb5e-singleValue {
  color: #222 !important;
  font-size: 1rem !important;
}

.name-builder-area .css-1u9des2-indicatorSeparator {
  display: none !important;
}

.name-builder-area .css-1xc3v61-indicatorContainer,
.name-builder-area .css-tj5bde-Svg {
  padding: 0 !important;
  height: 26px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 20px !important;
}

.name-builder-area .css-1xc3v61-indicatorContainer svg {
  width: 14px !important;
  height: 14px !important;
}

.name-builder-area .css-1jqq78o-placeholder {
  color: #888 !important;
  font-size: 1rem !important;
}

.name-builder-area .css-1n6sfyn-MenuList {
  max-height: 120px !important;
}

.name-builder-area .css-tr4s17-option {
  background-color: #bcd !important;
  color: #222 !important;
}

.name-builder-area .css-10wo9uf-option {
  background-color: #1976d2 !important;
  color: #fff !important;
}

.name-output-container {
  display: flex;
  align-items: center;
  margin-top: 8px;
  width: 100%;
  padding-right: 0;
  min-height: 28px;
  position: relative;
}

.name-output-area {
  flex: 1 1 0%;
  margin-right: 0;
  background: #fff !important;
  border: 1.5px solid #888 !important;
  padding: 2px 6px;
  min-height: 28px;
  font-size: 1rem;
  color: #222;
  box-sizing: border-box;
  white-space: nowrap !important;
  display: flex !important;
  align-items: center !important;
  flex: 1 1 auto !important;
  overflow: visible !important;
  min-width: 300px;
  width: 100%;
  position: relative;
  font-family: 'Noto Sans', 'Noto Sans Marathi', Arial, sans-serif !important;
}

.name-display-text {
  white-space: nowrap !important;
  display: inline !important;
  width: auto !important;
  overflow: visible !important;
  text-overflow: unset !important;
  font-size: 1rem !important;
  line-height: 1.2 !important;
  padding-right: 8px;
  font-family: 'Noto Sans', 'Noto Sans Marathi', Arial, sans-serif !important;
  font-weight: normal !important;
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: bidi-override !important;
}

.name-builder-footer {
  display: flex;
  align-items: stretch;
  justify-content: flex-end;
  margin: 0;
  padding: 0;
}

.name-builder-footer .id-button {
  height: 100%;
  min-height: 28px;
  align-self: stretch;
  box-sizing: border-box;
  margin: 0;
  padding: 0 8px;
}

.id-button {
  background: #d0d0d0 !important;
  border: 1.5px solid #888 !important;
  color: #222 !important;
  font-size: 1rem !important;
  min-width: 36px;
  height: 28px;
  padding: 0 8px;
  margin: 0;
  box-shadow: none;
  border-radius: 0 !important;
}

/* Identity Details Section */
.search-header {
  padding: 0.25rem;
  display: flex;
  align-items: center;
  background-color: #d0d0d0;
  border-bottom: 1px solid #999;
  border-top: 3px solid #0000cc;
  height: 28px;
}

.search-button {
  border: 2px solid #1976d2;
  background: #fff;
  color: #222;
  font-weight: bold;
  font-size: 1rem;
  transition: all 0.2s;
  outline: none;
  padding: 4px 12px;
}

.search-button:not(.active):hover,
.search-button:not(.active):focus {
  background: #e3f0fd;
  box-shadow: 0 0 0 2px #90caf9;
  cursor: pointer;
}

.search-button.active {
  border-color: #0066ff;
  box-shadow: 0 0 5px #0066ff;
}

.identity-search-grid {
  width: 100%;
  background-color: #fff;
  border: 1px solid #999;
}

.identity-header {
  display: flex;
  background-color: #e0e0ff;
  font-weight: normal;
  font-size: 0.85rem;
}

.identity-row, .identity-button-row {
  display: flex;
  background-color: #f5f5f5;
}

.identity-button-row {
  background-color: #d8d8d8;
  border-top: none;
  margin-top: auto;
  justify-content: flex-end;
  padding: 4px 6px 4px 0;
  display: flex;
  align-items: center;
}

.identity-cell, .button-cell {
  flex: 1;
  padding: 0.25rem;
  border: 1px solid #ccc;
  background-color: #f5f5f5;
}

.button-cell {
  background-color: transparent;
  padding: 2px;
  border: none;
  flex: 0 0 auto;
  width: auto;
}

.identity-header .identity-cell {
  background-color: #e0e0ff;
  color: #800080;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 0.25rem;
  background-color: #eee;
}

.search-buttons button {
  padding: 0.1rem 0.5rem;
  background-color: #ddd;
  border: 1px solid #999;
  cursor: pointer;
}

/* Address and Contact Side-by-Side Layout */
.address-contact-row {
  display: flex;
  width: 100%;
  gap: 2%;
}

.address-contact-row .form-section:first-child {
  flex: 0 0 60%;
  max-width: 60%;
}
.address-contact-row .form-section:last-child {
  flex: 0 0 35%;
  max-width: 35%;
}

/* Address Section */
.address-section {
  display: grid;
  grid-template-columns: minmax(0, 45%) minmax(0, 55%);
  gap: 0;
}

.address-inputs {
  background-color: #f0f0f0;
  border-right: 1px solid #ccc;
  padding: 0.5rem;
}

.city-search {
  width: 100%;
  padding: 0.3rem;
  background-color: #e0e0e0;
  border: 1px solid #ccc;
  text-align: center;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: bold;
}

.city-search-btn,
.city-search-btn:disabled {
  background: #e0e0e0;
  color: #222;
  border: 2px solid #e0e0e0;
  font-weight: bold;
  transition: all 0.2s;
  outline: none;
}
.city-search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.address-table-box.deactivated .address-table-input,
.address-table-box.deactivated .address-table-ok-btn {
  background: #f8f8f8;
  color: #888;
  cursor: not-allowed;
}
.address-table-box.deactivated .address-table-input:disabled {
  background: #f8f8f8;
  color: #888;
}

.address-fields {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0.25rem 0;
  align-items: center;
  margin-bottom: 0.5rem;
}

.address-label {
  font-size: 0.85rem;
  color: black;
  background-color: #00cccc;
  padding: 0.25rem;
  border: 1px solid #ccc;
  margin-right: 0.25rem;
  width: 80px;
}

.address-dropdown {
  position: relative;
}

.address-dropdown .form-control, 
.address-textareas .address-textarea {
  border: 1px solid #999;
  background-color: white;
}

.address-textareas {
  padding: 0.5rem;
}

.textarea-label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: normal;
  font-size: 0.9rem;
  color: #00f;
}

.address-textarea {
  width: 100%;
  height: 80px;
  resize: none;
  margin-bottom: 0.5rem;
  border: 1px solid #999;
}

/* Contact Details Section */
.contact-grid {
  display: grid;
  grid-template-columns: auto 1fr auto 1fr;
  gap: 0.5rem;
  align-items: start;
  padding: 0.5rem;
}

.contact-label {
  font-size: 0.9rem;
  padding: 0.25rem;
  color: #00f;
  white-space: nowrap;
  font-weight: bold;
}

.mobile-options {
  display: flex;
  align-items: center;
  margin-top: 0.25rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 0.25rem;
}

.form-control {
  padding: 2px 4px;
  border: 1px solid #999;
  background-color: white;
  width: 100%;
  font-size: 0.9rem;
  height: 22px;
  box-sizing: border-box;
}

.form-control:focus,
.form-control:active {
  background: #2378cd !important;
  color: #fff !important;
  border-color: #1976d2 !important;
  outline: none !important;
  box-shadow: 0 0 0 2px #90caf9;
  transition: background 0.2s, color 0.2s;
}

.form-control option,
.form-control optgroup {
  background: #fff !important;
  color: #222 !important;
}

/* PAN/UID Section */
.pan-uid-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding: 0.5rem;
  margin-top: 1rem;
}

.pan-input,
.uid-input {
  display: flex;
  gap: 0.25rem;
}

.pan-label,
.uid-label {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  color: blue;
  display: flex;
  justify-content: space-between;
  font-weight: bold;
}

.validate-btn {
  padding: 0.25rem 0.5rem;
  white-space: nowrap;
  font-size: 0.8rem;
  background-color: #e0e0e0;
  border: 1px solid #999;
  margin-left: 0.5rem;
}

.pan-container,
.uid-container {
  display: flex;
  flex-direction: column;
}

/* Individual character input styles for PAN and UID */
.pan-char-inputs, .uid-char-inputs {
  display: flex;
  gap: 1px;
}

.char-input {
  width: 1.5rem;
  height: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
  border: 1px solid #999;
  background-color: white;
  padding: 0;
}

/* Form Actions */
.form-actions {
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #ddd;
  border-top: 1px solid #999;
  position: relative;
  margin-top: 1rem;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 0.5rem 0;
  margin-right: 0.5rem;
}

.action-button {
  background: linear-gradient(to bottom, #f5f5f5, #ccc);
  border: 1px solid #999;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
  min-width: 120px;
  height: 2rem;
}

.record-info {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  background-color: #666;
  color: #8df8f8;
  padding: 0.25rem 0.5rem;
  width: 100%;
  height: 2rem;
}

.record-count {
  margin: 0 1rem;
}

.reset-message {
  flex: 1;
  text-align: center;
  font-size: 0.8rem;
  line-height: 1.2;
  color: #41f7f7;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .identity-search-grid,
  .identity-header,
  .identity-row,
  .address-section,
  .contact-grid,
  .pan-uid-section {
    display: block;
  }
  
  .identity-cell,
  .address-inputs,
  .address-textareas,
  .pan-container,
  .uid-container {
    margin-bottom: 0.5rem;
  }
  
  .form-group {
    width: 100%;
  }
}

/* Custom dropdown styling for native select elements */
.custom-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: none;
  background-color: white !important;
  cursor: pointer;
  padding-right: 24px !important;
}

.custom-dropdown::-ms-expand {
  display: none;
}

/* Add the colored button at the end of dropdown */
.prefix-select-wrapper, .name-builder-sex {
  position: relative;
}

.prefix-select-wrapper::after, .name-builder-sex::after {
  content: "";
  position: absolute;
  top: 1.5px;
  right: 1.5px;
  bottom: 1.5px;
  width: 20px;
  background-color: #d0d0d0;
  border-left: 1px solid #888;
  pointer-events: none;
}

/* Add dropdown arrow inside the button */
.prefix-select-wrapper::before, .name-builder-sex::before {
  content: "";
  position: absolute;
  top: 11px;
  right: 7px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #333;
  z-index: 1;
  pointer-events: none;
}

/* Style the Go and Ok buttons */
.identity-button-row .id-button:not(.create-new) {
  min-width: 40px;
  padding: 0 10px;
  background: linear-gradient(to bottom, #f5f5f5, #e5e5e5) !important;
  border: 1px solid #999 !important;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  margin: 0 3px;
  height: 24px;
  font-size: 0.9rem !important;
  color: #333 !important;
}

.name-builder-grid .id-button {
  margin: 4px 4px 0 4px;
  height: 28px;
  flex-shrink: 0;
  min-width: 40px;
  position: relative;
  z-index: 1;
}

/* Fix for react-select value containers to maintain fixed width */
.name-builder-area .css-1d8n9bt {
  padding: 0 !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.name-builder-area .css-319lph-ValueContainer {
  padding: 0 4px !important;
  margin: 0 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure fixed column width when interacting with react-select */
.name-input-wrapper .css-b62m3t-container,
.name-input-wrapper .css-1gtu0rj-container {
  width: 100% !important;
  max-width: 100% !important;
  position: relative !important;
}

/* Add specific rule for input field in focus/edit state */
.name-builder-area .css-qbdosj-Input {
  min-width: 1px !important;  /* Allow minimal shrink */
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix select height and text alignment in KYC Search panel */
.identity-cell select.form-control {
  height: 28px !important;
  min-height: 28px !important;
  font-size: 1rem !important;
  line-height: 1.2 !important;
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  vertical-align: middle !important;
}

.additional-identity-details-section {
  background: #fff;
  border: 1px solid #bbb;
  margin: 0 0 2px 0;
  padding: 2px 4px 2px 4px;
  font-size: 1rem;
  width: 100%;
  max-width: 1100px;
  min-width: 900px;
  box-sizing: border-box;
}

.additional-identity-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0;
}

.additional-label {
  color: #1a0dab;
  font-weight: bold;
  font-size: 1rem;
  margin-right: 4px;
  text-decoration: underline;
  min-width: unset;
  padding: 0 2px 0 0;
}

.additional-label.birth {
  text-decoration: none;
}

.additional-label.father {
  text-decoration: underline;
  position: relative;
  margin-right: 0;
}

.additional-sublabel {
  color: #1a0dab;
  font-size: 0.85em;
  font-weight: normal;
  margin-left: 2px;
  margin-right: 8px;
  text-decoration: none;
}

.additional-input {
  width: 32px;
  min-width: 28px;
  max-width: 40px;
  padding: 0 2px;
  font-size: 1rem;
  border: 1px solid #888;
  background: #fff;
  margin-right: 4px;
  box-sizing: border-box;
  height: 22px;
}

.additional-input[as="select"], .additional-input select {
  min-width: 80px;
  max-width: 120px;
  font-size: 1rem;
  padding: 0 2px;
  height: 22px;
}

.additional-input-year {
  width: 55px !important;
  min-width: 50px !important;
  max-width: 60px !important;
}

.vertical-separator {
  color: #888;
  font-weight: bold;
  margin: 0 2px;
  font-size: 1.1em;
  user-select: none;
  display: inline-block;
  vertical-align: middle;
}

.father-spouse-input {
  min-width: 320px;
  max-width: 600px;
  width: 100%;
  margin-right: 4px;
  font-size: 1rem;
  height: 22px;
  box-sizing: border-box;
}

.underlined {
  text-decoration: underline;
}

.additional-identity-table {
  display: grid;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr;
  width: 100%;
}

.additional-identity-header-row {
  display: grid;
  grid-template-columns: 150px 170px 2.5fr 170px;
  align-items: end;
  gap: 0;
  width: 100%;
  margin-bottom: 2px;
}

.additional-identity-header-row .additional-label {
  display: flex;
  align-items: flex-end;
  font-size: 1rem;
  min-height: 18px;
  padding-bottom: 0;
  margin-bottom: 0;
}

.additional-identity-input-row {
  display: grid;
  grid-template-columns: 150px 170px 2.5fr 170px;
  align-items: start;
  gap: 0;
  width: 100%;
}

.additional-date-group {
  display: flex;
  align-items: center;
  gap: 0;
  min-width: 0;
}

.additional-marital-group {
  display: flex;
  align-items: center;
  min-width: 0;
}

.additional-father-group {
  display: flex;
  align-items: center;
  min-width: 0;
}

.father-spouse-input {
  min-width: 320px;
  max-width: 600px;
  width: 100%;
  margin-right: 4px;
  font-size: 1rem;
  height: 22px;
  box-sizing: border-box;
}

/* Address Information Section - Redesigned */
.address-info-grid {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 16px;
  align-items: flex-start;
  width: 100%;
}

.address-box {
  border: 2px solid #00bcd4;
  background: #f8fcfd;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  min-width: 300px;
  max-width: 340px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.address-box-header {
  background: #555;
  color: #fff;
  font-weight: bold;
  text-align: center;
  padding: 0.4rem 0.5rem;
  border-bottom: 2px solid #00bcd4;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.address-box-fields {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem 0.5rem 0 0.5rem;
}

.address-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.15rem;
}

.address-label {
  width: 90px;
  min-width: 90px;
  font-size: 0.95rem;
  color: #00bcd4;
  font-weight: 500;
  background: #e0f7fa;
  border: 1px solid #b2ebf2;
  border-radius: 2px;
  padding: 2px 6px;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.address-input {
  flex: 1;
  min-width: 0;
  border: 1.5px solid #888;
  background: #fff;
  font-size: 1rem;
  padding: 2px 6px;
  border-radius: 2px;
  height: 28px;
}

.address-box-footer {
  margin-top: 0.5rem;
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  border-top: 1px solid #b2ebf2;
  display: flex;
  justify-content: flex-end;
}

.address-ok-btn {
  background: #00bcd4;
  color: #fff;
  border: none;
  border-radius: 2px;
  font-weight: bold;
  font-size: 1rem;
  padding: 4px 24px;
  cursor: pointer;
  transition: background 0.2s;
}
.address-ok-btn:hover {
  background: #0097a7;
}

.address-textarea-box {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  width: 100%;
}

.textarea-group {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.textarea-label {
  font-size: 0.95rem;
  color: #00bcd4;
  font-weight: 500;
  margin-bottom: 0.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.address-textarea {
  width: 100%;
  min-height: 60px;
  max-height: 90px;
  border: 1.5px solid #888;
  border-radius: 2px;
  font-size: 1rem;
  padding: 4px 8px;
  background: #fff;
  resize: none;
}

.copy-btn {
  margin-left: 8px;
  background: #e0f7fa;
  color: #00796b;
  border: 1px solid #00bcd4;
  border-radius: 2px;
  font-size: 0.85rem;
  padding: 2px 10px;
  cursor: pointer;
  transition: background 0.2s;
}
.copy-btn:hover {
  background: #b2ebf2;
  color: #004d40;
}

.address-info-row-exact {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  width: 100%;
  align-items: flex-start;
}

.address-table-box {
  border: 2px solid #00cccc;
  background: #fff;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  min-width: 270px;
  max-width: none;
  width: 65%;
  margin-right: 0;
  box-shadow: none;
}

.address-table-header {
  background: #00cccc;
  color: #fff;
  font-weight: bold;
  text-align: center;
  padding: 0.35rem 0.5rem;
  font-size: 1rem;
  border-bottom: 2px solid #b2ebf2;
  letter-spacing: 0.5px;
}

.address-table-fields {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0;
}

.address-table-row {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  border-bottom: 1px solid #b2ebf2;
  min-height: 36px;
}

.address-table-label {
  width: 90px;
  min-width: 90px;
  background: #00cccc;
  color: #fff;
  font-size: 0.95rem;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 2px 6px 0 6px;
  border-right: 1px solid #ccc;
  border-bottom: none;
  border-top: none;
  border-left: none;
}

.address-table-label-hi {
  font-size: 0.8em;
  color: #fff;
  font-weight: normal;
  margin-top: 0;
  margin-bottom: 2px;
  line-height: 1.1;
}

.address-table-input {
  flex: 1;
  min-width: 0;
  border: none;
  border-radius: 0;
  background: #fff;
  font-size: 1rem;
  padding: 2px 6px;
  height: 36px;
  color: #333;
  outline: none;
  box-shadow: none;
  border-right: 1px solid #ccc;
}

.address-table-row:last-child {
  border-bottom: none;
}

.address-table-footer {
  margin: 0;
  padding: 0;
  border-top: 2px solid #b2ebf2;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.address-table-ok-btn {
  background: #00cccc;
  color: #fff;
  border: none;
  border-radius: 0;
  font-weight: bold;
  font-size: 1rem;
  padding: 2px 6px;
  cursor: pointer;
  transition: background 0.2s;
  margin: 0;
  box-sizing: border-box;
  height: 36px;
  flex: 1;
}
.address-table-ok-btn:hover {
  background: #009fa3;
}

.address-table-textareas {
  flex: 1;
  width: auto;
  min-width: unset;
  max-width: unset;
  margin-left: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.address-table-textarea {
  min-width: 0;
  width: 100%;
  max-width: 100%;
  min-height: 50px;
  max-height: 70px;
  font-size: 1rem;
}

.name-output-footer-row {
  display: flex;
  width: 100%;
  align-items: stretch;
  gap: 0.5rem;
}

.english-label {
  color: #2a00d4;
  font-weight: bold; /* blue by default */
}

.name-builder-grid.disabled .english-label {
  color: #222 !important; /* black when disabled */
}

input[id^="react-select-"][type="text"] {
  opacity: 0 !important;
  pointer-events: none !important;
  height: 0 !important;
  min-height: 0 !important;
  max-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

.name-builder-occur {
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
  margin-right: 0;
}

.name-builder-grid.disabled,
.name-builder-sex.disabled {
  filter: blur(0.4px);
  pointer-events: none;
  user-select: none;
}

.additional-input:focus,
.additional-input:active {
  background: #367fc7 !important;
  color: #fff !important;
  border-color: #1976d2 !important;
  outline: none !important;
  box-shadow: 0 0 0 2px #90caf9;
  transition: background 0.2s, color 0.2s;
}

.additional-input option {
  background: #fff !important;
  color: #222 !important;
}

.address-table-empty-cell {
  flex: 1;
  border-left: none;
  background: #fff;
  min-height: 36px;
  border-right: 1px solid #ccc;
  padding: 2px 6px;
  margin: 0;
  box-sizing: border-box;
  height: 36px;
}

.address-table-row > *:last-child {
  border-right: none;
} 