import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Field, ErrorMessage, useFormikContext } from 'formik';
import { isValidPAN, isValidUID } from '../../utils/validations';

const PANUIDSection = () => {
  const { t } = useTranslation();
  const { values, setFieldValue } = useFormikContext();
  const [panValidationStatus, setPanValidationStatus] = useState(null);
  const [uidValidationStatus, setUidValidationStatus] = useState(null);

  // Handle PAN character input
  const handlePANCharChange = (index, value) => {
    const currentPAN = values.pan || '';
    let newPAN = currentPAN.split('');
    newPAN[index] = value.toUpperCase();
    setFieldValue('pan', newPAN.join(''));
  };
  
  // Handle UID character input
  const handleUIDCharChange = (index, value) => {
    const currentUID = values.uid || '';
    let newUID = currentUID.split('');
    newUID[index] = value;
    setFieldValue('uid', newUID.join(''));
  };
  
  // Generate individual character fields
  const renderPANChars = () => {
    const chars = [];
    const currentPAN = values.pan || '';
    
    for (let i = 0; i < 10; i++) {
      chars.push(
        <input
          key={`pan-${i}`}
          type="text"
          maxLength="1"
          className="char-input"
          value={currentPAN[i] || ''}
          onChange={(e) => handlePANCharChange(i, e.target.value)}
        />
      );
    }
    
    return chars;
  };
  
  // Generate individual character fields for UID
  const renderUIDChars = () => {
    const chars = [];
    const currentUID = values.uid || '';
    
    for (let i = 0; i < 12; i++) {
      chars.push(
        <input
          key={`uid-${i}`}
          type="text"
          maxLength="1"
          className="char-input"
          value={currentUID[i] || ''}
          onChange={(e) => handleUIDCharChange(i, e.target.value)}
        />
      );
    }
    
    return chars;
  };

  // Validate PAN
  const validatePAN = () => {
    if (!values.pan) {
      setPanValidationStatus('empty');
      return;
    }
    
    const isValid = isValidPAN(values.pan);
    setPanValidationStatus(isValid ? 'valid' : 'invalid');
  };

  // Validate UID
  const validateUID = () => {
    if (!values.uid) {
      setUidValidationStatus('empty');
      return;
    }
    
    const isValid = isValidUID(values.uid);
    setUidValidationStatus(isValid ? 'valid' : 'invalid');
  };

  // Get status message based on validation result
  const getStatusMessage = (status, type) => {
    if (status === null) return null;
    
    if (status === 'empty') {
      return <div className="validation-message warning">Please enter a {type} first</div>;
    }
    
    if (status === 'valid') {
      return <div className="validation-message success">{type} is valid</div>;
    }
    
    return <div className="validation-message error">{type} is invalid</div>;
  };

  return (
    <div className="form-section" style={{ width: '35%', marginLeft: 'auto', marginRight: '50px' }}>
      <div>
        <h3 className="form-section-title">
          7. PAN/UID/Membership <span className="bilingual-text">पैन/आधार/सदस्यता</span>
        </h3>
        <div className="form-section-content" style={{ background: '#f0f0f0', padding: '0.5rem' }}>
          <div className="pan-uid-section" style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
            <div className="pan-container">
              <div className="pan-label">
                PAN Number
                <button 
                  type="button" 
                  className="validate-btn"
                  onClick={validatePAN}
                >
                  Validate
                </button>
              </div>
              <div className="pan-char-inputs">
                {renderPANChars()}
              </div>
              <Field type="hidden" name="pan" />
              <ErrorMessage name="pan" component="div" className="form-error" />
              {getStatusMessage(panValidationStatus, 'PAN')}
            </div>
            
            <div className="uid-container">
              <div className="uid-label">
                UID (Aadhar) Number
                <button 
                  type="button" 
                  className="validate-btn"
                  onClick={validateUID}
                >
                  Validate
                </button>
              </div>
              <div className="uid-char-inputs">
                {renderUIDChars()}
              </div>
              <Field type="hidden" name="uid" />
              <ErrorMessage name="uid" component="div" className="form-error" />
              {getStatusMessage(uidValidationStatus, 'UID')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PANUIDSection; 