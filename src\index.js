import React from 'react';
import ReactDOM from 'react-dom/client';
import './i18n/i18n'; // Initialize i18n
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';

// Stagewise toolbar configuration
const stagewiseConfig = {
  plugins: []
};

// Create main app root
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// Initialize stagewise toolbar in development mode
if (process.env.NODE_ENV === 'development') {
  const { StagewiseToolbar } = require('@stagewise/toolbar-react');
  const toolbarRoot = ReactDOM.createRoot(document.createElement('div'));
  toolbarRoot.render(<StagewiseToolbar config={stagewiseConfig} />);
  document.body.appendChild(toolbarRoot.container);
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
