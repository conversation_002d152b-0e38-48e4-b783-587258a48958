.tab-container {
  margin-top: 0;
  width: 100%;
}

/* Custom styles for react-tabs */
.react-tabs__tab-list {
  border-bottom: none;
  margin: 0;
  padding: 0;
  background-color: #f0f0f0;
  display: flex;
}

.react-tabs__tab {
  display: inline-block;
  border: 1px solid #ccc;
  border-right: none;
  position: relative;
  list-style: none;
  padding: 0.4rem 1rem;
  cursor: pointer;
  font-weight: normal;
  background-color: #f0f0f0;
  border-radius: 0;
  margin: 0;
  text-align: center;
  flex: 1;
  color: #666;
  font-size: 0.85rem;
}

.react-tabs__tab:last-child {
  border-right: 1px solid #ccc;
}

.react-tabs__tab--selected {
  background-color: white;
  color: #333;
}

.react-tabs__tab:hover:not(.react-tabs__tab--selected) {
  background-color: #e0e0e0;
}

.react-tabs__tab-panel {
  display: none;
}

.react-tabs__tab-panel--selected {
  display: block;
  width: 100%;
}

/* Tab numbering */
.react-tabs__tab::before {
  content: attr(data-number) ". ";
  font-weight: normal;
}

/* Responsive tabs */
@media (max-width: 768px) {
  .react-tabs__tab {
    padding: 0.4rem;
    font-size: 0.8rem;
  }
}

.react-tabs {
  width: 100%;
} 