.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background-color: white;
  color: #000;
  border-bottom: 1px solid #ccc;
}

.branding {
  display: flex;
  align-items: center;
}

.app-version {
  color: #006400;
  font-size: 0.85rem;
  margin-right: 0.5rem;
}

.app-title {
  margin: 0;
  font-size: 0.85rem;
  font-weight: bold;
}

.language-switcher {
  display: flex;
  gap: 0.25rem;
}

.language-btn {
  padding: 0.2rem 0.4rem;
  background-color: #f0f0f0;
  border: 1px solid #999;
  color: #000;
  cursor: pointer;
  font-size: 0.8rem;
}

.language-btn.active {
  background-color: #ccc;
  border: 1px solid #666;
}

.language-btn:hover:not(.active) {
  background-color: #e0e0e0;
}

@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.25rem;
  }
} 