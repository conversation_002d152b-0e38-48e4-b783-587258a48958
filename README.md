# KYC Form Web Application

This web application digitizes the Know Your Customer (KYC) form process, inspired by the VSP-CRED system. It enables users to input, search, and manage customer KYC data using a multi-tab interface with bilingual support (English and Hindi) and responsive design.

## Features

- Multi-tab layout with KYC Form, Biometrics, and Related Accounts sections
- Bilingual UI with English and Hindi translations
- Responsive design that adapts to desktops and tablets
- KYC form with sections for Identity Details, Address Information, Contact Details, and PAN/UID
- Form validation for required fields and format validation
- Search functionality for existing KYC records
- Print form capability
- Data persistence using localStorage (for prototype)

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm 6.x or higher

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm start
```

4. Open your browser and navigate to [http://localhost:3000](http://localhost:3000)

## Usage

### KYC Form

The KYC Form tab allows you to:
- Enter customer identity details
- Search for existing KYC records
- Enter address information with copy functionality
- Add contact details with multiple mobile numbers
- Validate PAN and UID numbers
- Save and print KYC forms

### Biometrics

The Biometrics tab is a placeholder for biometric capture functionality, which would include:
- Fingerprint scanning
- Photo capture
- Signature capture

### Related Accounts

The Related Accounts tab demonstrates how account relationships could be managed, including:
- Searching for related accounts
- Viewing account details
- Adding new related accounts

## Technical Implementation

This application is built with:
- React for the UI components
- i18next for internationalization
- Formik and Yup for form management and validation
- localStorage for data persistence (in a production environment, this would use a backend database)

## Future Enhancements

- Backend integration with a database
- Enhanced biometric capture functionality
- Advanced search capabilities
- Role-based access control
- API integration for PAN and UID validation

## License

This project is licensed under the MIT License - see the LICENSE file for details.
