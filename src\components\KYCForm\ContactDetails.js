import React from 'react';
import { useTranslation } from 'react-i18next';
import { Field, useFormikContext } from 'formik';

const labelColor = '#0000ee';

const ContactDetails = () => {
  const { t } = useTranslation();
  const { values } = useFormikContext();

  return (
    <div className="form-section">
      <h3 className="form-section-title">
        3. {t('contactDetails.title')} <span className="bilingual-text">संपर्क-जानकारी</span>
      </h3>
      <div className="form-section-content">
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 2fr 2fr', gap: '8px', alignItems: 'center' }}>
          {/* Row 1 */}
          <div style={{ gridColumn: '1/2', color: labelColor }}>STD-Code</div>
          <div style={{ gridColumn: '2/3' }}>
            <a style={{ color: labelColor, cursor: 'default' }}>Mobile1 (Smart-Phone)</a>
          </div>
          <div style={{ gridColumn: '3/4', justifySelf: 'start' }}>
            <a style={{ color: labelColor, cursor: 'default' }}>Mobile2</a>
          </div>

          <div style={{ gridColumn: '1/2' }}>
            <Field id="stdCode" name="stdCode" type="text" className="form-control" />
          </div>
          <div style={{ gridColumn: '2/3' }}>
            <Field name="mobileNumbers[0]" type="text" className="form-control" placeholder="Mobile Number" />
          </div>
          <div style={{ gridColumn: '3/4' }}>
            <Field name="mobileNumbers[1]" type="text" className="form-control" placeholder="Mobile Number" />
          </div>

          {/* Row 2: Land-Line1 aligned with Mobile1 */}
          <div style={{ gridColumn: '2/3', marginTop: '8px' }}>
            <a style={{ color: labelColor, cursor: 'default' }}>Land-Line1</a>
            <Field id="landline" name="landline" type="text" className="form-control" />
          </div>

          {/* Row 3 */}
          <div style={{ gridColumn: '1/2', marginTop: '8px' }}>
            <a style={{ color: labelColor, cursor: 'default' }}>E-mail ID</a>
          </div>
          <div style={{ gridColumn: '2/4', marginTop: '8px' }}>
            <Field id="email" name="email" type="email" className="form-control" />
          </div>

          {/* Row 4 */}
          <div style={{ gridColumn: '1/2', marginTop: '8px' }}>
            <a style={{ color: labelColor, cursor: 'default' }}>Website Address</a>
          </div>
          <div style={{ gridColumn: '2/4', marginTop: '8px' }}>
            <Field id="website" name="website" type="text" className="form-control" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactDetails; 