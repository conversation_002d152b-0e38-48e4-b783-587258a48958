// Storage keys
const STORAGE_KEYS = {
  KYC_RECORDS: 'kycRecords'
};

/**
 * Get KYC records from localStorage
 * @returns {Array} Array of KYC records
 */
export const getKYCRecords = () => {
  const records = localStorage.getItem(STORAGE_KEYS.KYC_RECORDS);
  return records ? JSON.parse(records) : [];
};

/**
 * Save a new KYC record
 * @param {Object} record KYC record to save
 * @returns {Object} Saved record with generated ID
 */
export const saveKYCRecord = (record) => {
  const records = getKYCRecords();
  const newRecord = {
    ...record,
    id: `KYC${Date.now()}`,
    createdAt: new Date().toISOString()
  };
  
  records.push(newRecord);
  localStorage.setItem(STORAGE_KEYS.KYC_RECORDS, JSON.stringify(records));
  
  return newRecord;
};

/**
 * Search KYC records by criteria
 * @param {Object} criteria Search criteria
 * @returns {Array} Matching KYC records
 */
export const searchKYCRecords = (criteria) => {
  const records = getKYCRecords();
  
  if (!criteria || Object.keys(criteria).length === 0) {
    return records;
  }
  
  return records.filter(record => {
    return Object.entries(criteria).every(([key, value]) => {
      if (!value) return true;
      
      const recordValue = record[key]?.toString().toLowerCase();
      const searchValue = value.toString().toLowerCase();
      
      return recordValue && recordValue.includes(searchValue);
    });
  });
};

/**
 * Clear all KYC records (for testing purposes)
 */
export const clearAllRecords = () => {
  localStorage.removeItem(STORAGE_KEYS.KYC_RECORDS);
}; 