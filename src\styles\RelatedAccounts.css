.related-accounts-container {
  padding: 1rem;
}

.related-accounts-container h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.search-container {
  margin-bottom: 1.5rem;
}

.search-input {
  max-width: 500px;
}

.accounts-list {
  border: 1px solid var(--medium-gray);
  border-radius: 4px;
  overflow: hidden;
}

.accounts-header {
  display: flex;
  background-color: var(--light-gray);
  font-weight: bold;
  border-bottom: 2px solid var(--medium-gray);
}

.account-row {
  display: flex;
  border-bottom: 1px solid var(--medium-gray);
}

.account-row:last-child {
  border-bottom: none;
}

.account-row:hover {
  background-color: var(--light-gray);
}

.account-column {
  flex: 1;
  padding: 1rem;
  display: flex;
  align-items: center;
}

.view-btn {
  padding: 0.4rem 0.8rem;
  font-size: 0.9rem;
}

.no-accounts {
  padding: 2rem;
  text-align: center;
  color: var(--dark-gray);
}

.add-account-section {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.implementation-note {
  margin-top: 2rem;
  padding: 1rem;
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid var(--warning-color);
  border-radius: 4px;
}

@media (max-width: 768px) {
  .accounts-header,
  .account-row {
    flex-direction: column;
  }
  
  .account-column {
    border-bottom: 1px solid var(--medium-gray);
    padding: 0.75rem;
  }
  
  .account-column:first-child {
    font-weight: bold;
    background-color: var(--light-gray);
  }
  
  .account-row .account-column:last-child {
    border-bottom: none;
  }
  
  .accounts-header {
    display: none;
  }
} 