import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Field, useFormikContext } from 'formik';

const MARATHI_MAP = {
  state: {
    'maharashtra': 'महाराष्ट्र',
    'delhi': 'दिल्ली',
    'karnataka': 'कर्नाटक',
    'tamilnadu': 'तमिळनाडू',
    'gujarat': 'गुजरात'
  },
  district: {
    'mumbai': 'मुंबई',
    'pune': 'पुणे',
    'thane': 'ठाणे',
    'nagpur': 'नागपूर'
  },
  taluka: {
    'taluka1': 'तालुका १',
    'taluka2': 'तालुका २',
    'taluka3': 'तालुका ३'
  },
  city: {
    'kanadgaon': 'कनडगाव',
    'pune': 'पुणे',
    'mumbai': 'मुंबई',
    'nashik': 'नाशिक'
  }
};

const AddressInformation = () => {
  const { t } = useTranslation();
  const { values, setFieldValue } = useFormikContext();
  const [isActive, setIsActive] = useState(false);

  // Copy correspondence address to permanent address
  const handleCopyAddress = () => {
    setFieldValue('permanentAddress', values.correspondenceAddress);
  };

  // Handler for City Search button
  const handleCitySearch = () => {
    setIsActive(true);
  };

  return (
    <div className="form-section">
      <h3 className="form-section-title">
        2. {t('addressInformation.title')} <span className="bilingual-text">पता</span>
      </h3>
      <div className="address-info-row-exact">
        {/* Left: Address Box as Table */}
        <div className={`address-table-box${isActive ? ' active' : ' deactivated'}`}> 
          <button
            type="button"
            className={`address-table-header city-search-btn${isActive ? ' active' : ''}`}
            onClick={handleCitySearch}
            disabled={isActive}
            tabIndex={0}
          >
            City Search
          </button>
          <div className="address-table-fields">
            <div className="address-table-row">
              <div className="address-table-label">
                State
                <div className="address-table-label-hi">राज्य</div>
              </div>
              <Field
                id="state"
                name="state"
                as="select"
                className="form-control address-table-input"
                disabled={!isActive}
                style={{ height: '40px' }}
              >
                <option value="">-</option>
                <option value="maharashtra">Maharashtra</option>
                <option value="delhi">Delhi</option>
                <option value="karnataka">Karnataka</option>
                <option value="tamilnadu">Tamil Nadu</option>
                <option value="gujarat">Gujarat</option>
              </Field>
              <div className="address-table-empty-cell">{MARATHI_MAP.state[values.state] || ''}</div>
            </div>
            <div className="address-table-row">
              <div className="address-table-label">
                District
                <div className="address-table-label-hi">जिला</div>
              </div>
              <Field
                id="district"
                name="district"
                as="select"
                className="form-control address-table-input"
                disabled={!isActive}
                style={{ height: '40px' }}
              >
                <option value="">-</option>
                <option value="mumbai">Mumbai</option>
                <option value="pune">Pune</option>
                <option value="thane">Thane</option>
                <option value="nagpur">Nagpur</option>
              </Field>
              <div className="address-table-empty-cell">{MARATHI_MAP.district[values.district] || ''}</div>
            </div>
            <div className="address-table-row">
              <div className="address-table-label">
                Taluka
                <div className="address-table-label-hi">तालुका</div>
              </div>
              <Field
                id="taluka"
                name="taluka"
                as="select"
                className="form-control address-table-input"
                disabled={!isActive}
                style={{ height: '40px' }}
              >
                <option value="">-</option>
                <option value="taluka1">Taluka 1</option>
                <option value="taluka2">Taluka 2</option>
                <option value="taluka3">Taluka 3</option>
              </Field>
              <div className="address-table-empty-cell">{MARATHI_MAP.taluka[values.taluka] || ''}</div>
            </div>
            <div className="address-table-row">
              <div className="address-table-label">
                City
                <div className="address-table-label-hi">शहर</div>
              </div>
              <Field
                id="city"
                name="city"
                as="select"
                className="form-control address-table-input"
                disabled={!isActive}
                style={{ height: '40px' }}
              >
                <option value="">-</option>
                <option value="kanadgaon">Kanadgaon</option>
                <option value="pune">Pune</option>
                <option value="mumbai">Mumbai</option>
                <option value="nashik">Nashik</option>
              </Field>
              <div className="address-table-empty-cell">{MARATHI_MAP.city[values.city] || ''}</div>
            </div>
            <div className="address-table-row">
              <div className="address-table-label">
                Pin-Cd
                <div className="address-table-label-hi">पिन-कोड</div>
              </div>
              <Field
                id="pinCode"
                name="pinCode"
                type="text"
                className="form-control address-table-input"
                disabled={!isActive}
                style={{ height: '40px' }}
              />
              <button type="button" className="address-table-ok-btn" disabled={!isActive}>Ok</button>
            </div>
          </div>
        </div>
        {/* Right: Address Textareas */}
        <div className="address-table-textareas">
          <div className="address-table-textarea-group">
            <label htmlFor="correspondenceAddress" className="address-table-textarea-label" style={{ color: '#0000ee' }}>
              Correspondence-Address
            </label>
            <Field
              id="correspondenceAddress"
              name="correspondenceAddress"
              as="textarea"
              className="address-table-textarea"
              rows="4"
            />
          </div>
          <div className="address-table-textarea-group">
            <label htmlFor="permanentAddress" className="address-table-textarea-label" style={{ color: '#0000ee' }}>
              Permanent-Address
              <button type="button" className="address-table-copy-btn" onClick={handleCopyAddress} title="Copy from Correspondence">Copy</button>
            </label>
            <Field
              id="permanentAddress"
              name="permanentAddress"
              as="textarea"
              className="address-table-textarea"
              rows="4"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressInformation; 