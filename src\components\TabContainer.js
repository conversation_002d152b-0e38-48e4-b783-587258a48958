import React from 'react';
import { Tab, Tabs, <PERSON>b<PERSON><PERSON>, TabPanel } from 'react-tabs';
import { useTranslation } from 'react-i18next';
import KYCForm from './KYCForm/KYCForm';
import Biometrics from './Biometrics/Biometrics';
import RelatedAccounts from './RelatedAccounts/RelatedAccounts';
import '../styles/TabContainer.css';
import 'react-tabs/style/react-tabs.css';

const TabContainer = () => {
  const { t } = useTranslation();

  return (
    <div className="tab-container">
      <Tabs>
        <TabList>
          <Tab data-number="1">{t('tabs.kycForm')}</Tab>
          <Tab data-number="2">{t('tabs.biometrics')}</Tab>
          <Tab data-number="3">{t('tabs.relatedAccounts')}</Tab>
        </TabList>

        <TabPanel>
          <div className="tab-content">
            <KYCForm />
          </div>
        </TabPanel>
        
        <TabPanel>
          <div className="tab-content">
            <Biometrics />
          </div>
        </TabPanel>
        
        <TabPanel>
          <div className="tab-content">
            <RelatedAccounts />
          </div>
        </TabPanel>
      </Tabs>
    </div>
  );
};

export default TabContainer; 