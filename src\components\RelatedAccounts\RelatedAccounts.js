import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import '../../styles/RelatedAccounts.css';

const RelatedAccounts = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  
  // Sample data - in a real application this would come from an API or service
  const sampleAccounts = [
    { id: 'ACC123456', name: '<PERSON>', relationshipType: 'Primary', accountType: 'Savings' },
    { id: 'ACC789012', name: '<PERSON>', relationshipType: 'Spouse', accountType: 'Current' },
    { id: 'ACC345678', name: '<PERSON>', relationshipType: 'Son', accountType: 'Fixed Deposit' },
  ];
  
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };
  
  const filteredAccounts = sampleAccounts.filter(account => 
    account.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="related-accounts-container">
      <h2>{t('tabs.relatedAccounts')}</h2>
      
      <div className="search-container">
        <input
          type="text"
          className="form-control search-input"
          placeholder="Search by Account ID or Name"
          value={searchTerm}
          onChange={handleSearch}
        />
      </div>
      
      <div className="accounts-list">
        <div className="accounts-header">
          <div className="account-column">Account ID</div>
          <div className="account-column">Name</div>
          <div className="account-column">Relationship</div>
          <div className="account-column">Account Type</div>
          <div className="account-column">Actions</div>
        </div>
        
        {filteredAccounts.length > 0 ? (
          filteredAccounts.map(account => (
            <div className="account-row" key={account.id}>
              <div className="account-column">{account.id}</div>
              <div className="account-column">{account.name}</div>
              <div className="account-column">{account.relationshipType}</div>
              <div className="account-column">{account.accountType}</div>
              <div className="account-column">
                <button className="btn btn-secondary view-btn">View</button>
              </div>
            </div>
          ))
        ) : (
          <div className="no-accounts">No related accounts found</div>
        )}
      </div>
      
      <div className="add-account-section">
        <button className="btn btn-primary">Add Related Account</button>
      </div>
      
      <p className="implementation-note">
        Note: This is a placeholder for the related accounts functionality. In a real implementation,
        this would integrate with an account management system.
      </p>
    </div>
  );
};

export default RelatedAccounts; 