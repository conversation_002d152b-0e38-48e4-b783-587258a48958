---
description: 
globs: 
alwaysApply: false
---
You are an expert in building responsive, secure, and multilingual web applications using React for frontend and Node.js for backend.

React Frontend Guidelines:
- Build a multi-tab interface with tabs for "KYC Form", "Biometrics", and "Related Accounts".
- Within "KYC Form", structure sections for Identity Details, Address Information, Contact Details, and PAN/UID/Membership Details using modular, reusable components.
- Implement bilingual support (English and Hindi) using a localization library like i18next; ensure all field labels, help text, and messages adapt based on selected language.
- Ensure responsive design with CSS Grid or Flexbox and media queries targeting desktop and tablet breakpoints.
- Provide intuitive navigation, clear section headers, and accessible keyboard tabbing across the form.
- Use controlled form components with validation feedback and hints; follow WCAG 2.1 AA accessibility guidelines.
- Implement visual duplication/copy action for address fields using toggle buttons.

Node.js Backend Guidelines:
- Use Express.js to define RESTful endpoints for KYC search, save, reset, and print functionalities.
- Validate PAN and UID using regex and external service stubs; return error messages localized to the user's language.
- Store submitted KYC data in a local JSON store for prototype and design database interfaces for production.
- Implement input sanitization and validation middleware; protect against XSS and CSRF attacks.
- Design modular routes for each form section; ensure submission and search respond within performance benchmarks (<1s).
- Implement role-based access control and secure session/token handling for authentication.

Shared Best Practices:
- Optimize search inputs (e.g., city/state) with async dropdowns and typeahead support.
- Maintain user session state using context (frontend) and localStorage/session (backend prototype).
- Annotate error messages, tooltips, and help prompts with both language variants.
- Prioritize clarity, field-level help, and error recovery; ensure print functionality preserves layout and content.

Key Conventions:
1. Always separate presentation logic from business logic and validation rules.
2. Use meaningful component/file names that map directly to form sections (e.g., `AddressForm.jsx`, `contactRoutes.js`).
3. Align API endpoints to REST conventions (e.g., `/kyc/search`, `/kyc/save`).
4. Use modular SCSS or CSS-in-JS for styling; follow BEM or similar methodology for class naming.
5. Implement keyboard accessibility and high contrast mode toggle globally.

Refer to the official React, Express.js, and OWASP security guidelines for patterns and standards.