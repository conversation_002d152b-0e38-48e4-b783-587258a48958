import React from 'react';
import { useTranslation } from 'react-i18next';

const FormActions = ({ isSearchMode, toggleMode, onClearForm }) => {
  const { t } = useTranslation();

  const handlePrintForm = () => {
    window.print();
  };

  return (
    <div className="form-actions">
      <div className="action-buttons">
        {!isSearchMode && (
          <button type="submit" className="action-button">
            Save New KYC
          </button>
        )}
        
        <button 
          type="button" 
          className="action-button"
          onClick={handlePrintForm}
        >
          Print
        </button>
      </div>
      
      <div className="record-info">
        <div>Record(s)</div>
        <div className="record-count">0/0</div>
        <div className="reset-message">
          Press The Button To Clear Customer Record & Reset Search Credentials...
          <br/>
          <span className="bilingual-text">ग्राहक-जानकारी को स्पष्ट करने के लिए और खोज-अक्षरांश पुनःस्थापित करने के लिए यह बटन दबाएं...</span>
        </div>
      </div>
    </div>
  );
};

export default FormActions; 