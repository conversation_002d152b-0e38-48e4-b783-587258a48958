/* Global styles */
:root {
  --primary-color: #1976d2;
  --secondary-color: #f50057;
  --light-gray: #f5f5f5;
  --medium-gray: #e0e0e0;
  --dark-gray: #757575;
  --text-color: #333;
  --success-color: #4caf50;
  --warning-color: #ffc107;
  --error-color: #f44336;
  --header-bg: #5a5a5a;
  --section-header-bg: #6b6b6b;
  --section-header-color: white;
  --input-bg: #f0f0f0;
  --border-color: #999;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Arial', sans-serif;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  line-height: 1.5;
  background-color: white;
}

.App {
  width: 100%;
  min-height: 100vh;
}

.container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}

.tab-content {
  background-color: white;
  padding: 0;
  border: 1px solid var(--border-color);
  border-top: none;
  width: 100%;
}

/* Form Styles */
.form-section {
  margin-bottom: 1rem;
  padding: 0;
  background-color: white;
}

.form-section-title {
  margin: 0;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  color: var(--section-header-color);
  background-color: var(--section-header-bg);
  font-weight: bold;
}

.form-section-content {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-top: none;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0.5rem 0;
}

.form-group {
  flex: 1;
  min-width: 150px;
  padding: 0 0.5rem;
  margin-bottom: 0.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  font-size: 0.9rem;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.form-error {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Button Styles */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0;
  font-size: 0.9rem;
  font-weight: normal;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: var(--light-gray);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-primary:hover {
  background-color: var(--medium-gray);
}

.btn-secondary {
  background-color: var(--light-gray);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--medium-gray);
}

.btn-danger {
  background-color: var(--light-gray);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-danger:hover {
  background-color: var(--medium-gray);
}

.btn-group {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

/* Bilingual text */
.bilingual-text {
  font-size: 0.85rem;
} 